import {
  Button,
  TextField,
  Autocomplete,
  Paper,
  Box,
  Chip,
  Tooltip,
  Fade,
} from "@mui/material";
import {
  Public,
  Analytics,
  Visibility,
  Search as SearchIcon,
  TrendingUp,
} from "@mui/icons-material";
import React, { FormEvent, useContext } from "react";
import { useQueryClient } from "react-query";
import styled from "styled-components";
import { useChannels } from "../hooks/useChannels";
import { store } from "../store";
import { Docs } from "./Docs";
import { Optout } from "./Optout";
import { Settings } from "./Settings";

const FiltersWrapper = styled.div`
  padding: 16px;
  background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
`;

export function Filters() {
  const { setCurrents, setShowAnalytics, setShowUserTracker, state } =
    useContext(store);
  const queryClient = useQueryClient();
  const channels = useChannels();

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (e.target instanceof HTMLFormElement) {
      const data = new FormData(e.target);

      const channel = data.get("channel") as string | null;
      const username = data.get("username") as string | null;

      queryClient.invalidateQueries([
        "log",
        { channel: channel?.toLowerCase(), username: username?.toLowerCase() },
      ]);

      setCurrents(channel, username);
    }
  };

  const handleGlobalSearch = () => {
    queryClient.invalidateQueries();

    // Update state to navigate to global search
    // Use setTimeout to ensure state updates are processed in the right order
    setShowAnalytics(false);
    setShowUserTracker(false);

    // Clear channel/username to trigger global search view
    setTimeout(() => {
      setCurrents(null, null);
    }, 0);
  };

  const handleAnalytics = () => {
    queryClient.invalidateQueries();
    setShowUserTracker(false);
    setCurrents(null, null);
    setShowAnalytics(true);
  };

  const handleUserTracker = () => {
    queryClient.invalidateQueries();
    setShowAnalytics(false);
    setCurrents(null, null);
    setShowUserTracker(true);
  };

  return (
    <FiltersWrapper>
      <Fade in timeout={800}>
        <Paper
          elevation={8}
          sx={{
            p: 3,
            background: "rgba(255, 255, 255, 0.95)",
            backdropFilter: "blur(10px)",
            borderRadius: 3,
            maxWidth: 1200,
            margin: "0 auto",
          }}
        >
          <Box component="form" onSubmit={handleSubmit} action="none">
            <Box
              display="flex"
              flexWrap="wrap"
              alignItems="center"
              gap={2}
              justifyContent="center"
            >
              {/* Channel Input */}
              <Autocomplete
                id="autocomplete-channels"
                options={channels.map((channel) => channel.name)}
                sx={{ minWidth: 200, flexGrow: 1, maxWidth: 250 }}
                defaultValue={state.currentChannel}
                getOptionLabel={(channel: string) => channel}
                clearOnBlur={false}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    name="channel"
                    label="Channel or ID"
                    variant="outlined"
                    size="small"
                    autoFocus={state.currentChannel === null}
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: (
                        <SearchIcon sx={{ mr: 1, color: "text.secondary" }} />
                      ),
                    }}
                  />
                )}
                renderOption={(props, option) => (
                  <Box component="li" {...props}>
                    <Chip label={`#${option}`} size="small" sx={{ mr: 1 }} />
                    {option}
                  </Box>
                )}
              />

              {/* Username Input */}
              <TextField
                error={state.error}
                name="username"
                label="Username or ID"
                variant="outlined"
                size="small"
                autoComplete="off"
                defaultValue={state.currentUsername}
                autoFocus={
                  state.currentChannel !== null &&
                  state.currentUsername === null
                }
                sx={{ minWidth: 200, flexGrow: 1, maxWidth: 250 }}
                InputProps={{
                  startAdornment: (
                    <SearchIcon sx={{ mr: 1, color: "text.secondary" }} />
                  ),
                }}
              />

              {/* Load Button */}
              <Tooltip title="Load messages for the specified channel and user">
                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  type="submit"
                  sx={{
                    minWidth: 100,
                    borderRadius: 2,
                    textTransform: "none",
                    fontWeight: "bold",
                  }}
                >
                  Load
                </Button>
              </Tooltip>

              {/* Navigation Buttons */}
              <Box display="flex" gap={1} flexWrap="wrap">
                <Tooltip title="Search across all channels">
                  <Button
                    variant="outlined"
                    size="large"
                    onClick={handleGlobalSearch}
                    startIcon={<Public />}
                    sx={{
                      borderRadius: 2,
                      textTransform: "none",
                      borderColor: "primary.main",
                      color: "primary.main",
                      "&:hover": {
                        borderColor: "primary.dark",
                        backgroundColor: "rgba(25, 118, 210, 0.1)",
                      },
                    }}
                  >
                    Global Search
                  </Button>
                </Tooltip>

                <Tooltip title="View analytics dashboard">
                  <Button
                    variant="outlined"
                    size="large"
                    onClick={handleAnalytics}
                    startIcon={<Analytics />}
                    sx={{
                      borderRadius: 2,
                      textTransform: "none",
                      borderColor: "secondary.main",
                      color: "secondary.main",
                      "&:hover": {
                        borderColor: "secondary.dark",
                        backgroundColor: "rgba(156, 39, 176, 0.1)",
                      },
                    }}
                  >
                    Analytics
                  </Button>
                </Tooltip>

                <Tooltip title="Track specific users across channels">
                  <Button
                    variant="outlined"
                    size="large"
                    onClick={handleUserTracker}
                    startIcon={<Visibility />}
                    sx={{
                      borderRadius: 2,
                      textTransform: "none",
                      borderColor: "info.main",
                      color: "info.main",
                      "&:hover": {
                        borderColor: "info.dark",
                        backgroundColor: "rgba(2, 136, 209, 0.1)",
                      },
                    }}
                  >
                    User Tracker
                  </Button>
                </Tooltip>
              </Box>

              {/* Settings and Options */}
              <Box display="flex" gap={1}>
                <Settings />
                <Docs />
                <Optout />
              </Box>
            </Box>
          </Box>
        </Paper>
      </Fade>
    </FiltersWrapper>
  );
}
