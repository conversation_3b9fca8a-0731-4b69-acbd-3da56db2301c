export interface UserLogResponse {
  messages: BasicLogMessage[];
}

export interface LogMessage extends Omit<BasicLogMessage, "timestamp"> {
  timestamp: Date;
  emotes: Array<Emote>;
}

export interface BasicLogMessage {
  text: string;
  displayName: string;
  timestamp: string;
  id: string;
  tags: Record<string, string>;
}

export interface FullLogMessage extends BasicLogMessage {
  username: string;
  channel: string;
  raw: string;
  type: number;
  emotes?: Array<Emote>;
}

export interface Emote {
  startIndex: number;
  endIndex: number;
  code: string;
  id: string;
}
