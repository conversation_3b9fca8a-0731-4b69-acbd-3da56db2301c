import React, { useState, useContext, useMemo } from "react";
import {
  Box,
  Typography,
  Paper,
  Chip,
  Button,
  CircularProgress,
  Alert,
  Pagination,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Tooltip,
  Autocomplete,
  TextField,
  Card,
  CardContent,
  Grid,
  Divider,
  Avatar,
} from "@mui/material";
import {
  Download,
  Share,
  FilterList,
  Sort,
  Visibility,
  VisibilityOff,
  Forum,
  Person,
  Clear,
  TrendingUp,
} from "@mui/icons-material";
import styled from "styled-components";
import { LogMessage, FullLogMessage } from "../types/log";
import { LogLine } from "./LogLine";
import { store } from "../store";
import { parseEmotes } from "../services/parseEmotes";

const SearchResultsContainer = styled(Paper)`
  margin: 1rem 0;
  padding: 1rem;
`;

const ResultsHeader = styled(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
`;

const ResultsStats = styled(Box)`
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
`;

const ResultsList = styled(Box)`
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #333;
  border-radius: 4px;
  background: #1a1a1a;
`;

const ResultItem = styled(Box)`
  padding: 0.5rem;
  border-bottom: 1px solid #333;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #2a2a2a;
  }
`;

const HighlightedText = styled.span`
  background: #ffeb3b;
  color: #000;
  padding: 0 2px;
  border-radius: 2px;
`;

interface SearchResultsProps {
  results: FullLogMessage[] | undefined;
  isLoading: boolean;
  error: any;
  searchQuery?: string;
  totalResults?: number;
  onExport?: () => void;
  onShare?: () => void;
  onClearSearch?: () => void;
  isGlobalSearch?: boolean;
}

const ITEMS_PER_PAGE = 50;

export function SearchResults({
  results,
  isLoading,
  error,
  searchQuery,
  totalResults,
  onExport,
  onShare,
  onClearSearch,
  isGlobalSearch = false,
}: SearchResultsProps) {
  const { state } = useContext(store);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState<
    "timestamp" | "relevance" | "channel" | "user"
  >("timestamp");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [showContext, setShowContext] = useState(false);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  // Extract unique channels and users from results for filtering
  const availableChannels = useMemo(() => {
    if (!results) return [];
    const channels = [...new Set(results.map((msg) => msg.channel))].sort();
    return channels;
  }, [results]);

  const availableUsers = useMemo(() => {
    if (!results) return [];
    const users = [
      ...new Set(results.map((msg) => msg.username || msg.displayName)),
    ].sort();
    return users;
  }, [results]);

  // Filter results based on selected channels and users
  const filteredResults = useMemo(() => {
    if (!results) return [];

    let filtered = results;

    if (selectedChannels.length > 0) {
      filtered = filtered.filter((msg) =>
        selectedChannels.includes(msg.channel)
      );
    }

    if (selectedUsers.length > 0) {
      filtered = filtered.filter(
        (msg) =>
          selectedUsers.includes(msg.username) ||
          selectedUsers.includes(msg.displayName)
      );
    }

    return filtered;
  }, [results, selectedChannels, selectedUsers]);

  if (isLoading) {
    return (
      <SearchResultsContainer>
        <Box display="flex" justifyContent="center" alignItems="center" p={4}>
          <CircularProgress />
          <Typography variant="body1" sx={{ ml: 2 }}>
            Searching...
          </Typography>
        </Box>
      </SearchResultsContainer>
    );
  }

  if (error) {
    return (
      <SearchResultsContainer>
        <Alert severity="error">
          Search failed: {error.message || "Unknown error"}
        </Alert>
      </SearchResultsContainer>
    );
  }

  if (!results || results.length === 0) {
    return (
      <SearchResultsContainer>
        <Alert severity="info">No results found for "{searchQuery}"</Alert>
      </SearchResultsContainer>
    );
  }

  if (
    filteredResults.length === 0 &&
    (selectedChannels.length > 0 || selectedUsers.length > 0)
  ) {
    return (
      <SearchResultsContainer>
        <Alert severity="info">
          No results match the selected filters. Try adjusting your channel or
          user filters.
        </Alert>
      </SearchResultsContainer>
    );
  }

  // Sort filtered results
  const sortedResults = [...filteredResults].sort((a, b) => {
    if (sortBy === "timestamp") {
      const timeA = new Date(a.timestamp).getTime();
      const timeB = new Date(b.timestamp).getTime();
      return sortOrder === "asc" ? timeA - timeB : timeB - timeA;
    } else if (sortBy === "channel") {
      const channelA = a.channel.toLowerCase();
      const channelB = b.channel.toLowerCase();
      return sortOrder === "asc"
        ? channelA.localeCompare(channelB)
        : channelB.localeCompare(channelA);
    } else if (sortBy === "user") {
      const userA = (a.username || a.displayName).toLowerCase();
      const userB = (b.username || b.displayName).toLowerCase();
      return sortOrder === "asc"
        ? userA.localeCompare(userB)
        : userB.localeCompare(userA);
    }
    // For relevance, we could implement a scoring system
    return 0;
  });

  // Paginate results
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const paginatedResults = sortedResults.slice(startIndex, endIndex);
  const totalPages = Math.ceil(sortedResults.length / ITEMS_PER_PAGE);

  const highlightText = (text: string, query: string) => {
    if (!query) return text;

    const regex = new RegExp(
      `(${query.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
      "gi"
    );
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <HighlightedText key={index}>{part}</HighlightedText>
      ) : (
        part
      )
    );
  };

  const handleExport = () => {
    if (onExport) {
      onExport();
    } else {
      // Default export as JSON
      const dataStr = JSON.stringify(results, null, 2);
      const dataBlob = new Blob([dataStr], { type: "application/json" });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `search-results-${
        new Date().toISOString().split("T")[0]
      }.json`;
      link.click();
      URL.revokeObjectURL(url);
    }
  };

  // Channel and user statistics
  const channelStats = useMemo(() => {
    const stats = new Map<string, number>();
    filteredResults.forEach((msg) => {
      stats.set(msg.channel, (stats.get(msg.channel) || 0) + 1);
    });
    return Array.from(stats.entries())
      .map(([channel, count]) => ({ channel, count }))
      .sort((a, b) => b.count - a.count);
  }, [filteredResults]);

  const userStats = useMemo(() => {
    const stats = new Map<string, number>();
    filteredResults.forEach((msg) => {
      const user = msg.username || msg.displayName;
      stats.set(user, (stats.get(user) || 0) + 1);
    });
    return Array.from(stats.entries())
      .map(([user, count]) => ({ user, count }))
      .sort((a, b) => b.count - a.count);
  }, [filteredResults]);

  return (
    <SearchResultsContainer>
      {/* Enhanced Results Header with Statistics */}
      <ResultsHeader>
        <ResultsStats>
          <Typography variant="h6">Search Results</Typography>
          <Chip
            label={`${filteredResults.length} results`}
            color="primary"
            variant="outlined"
          />
          {filteredResults.length !== results.length && (
            <Chip
              label={`${results.length} total`}
              color="secondary"
              variant="outlined"
            />
          )}
          {isGlobalSearch && (
            <>
              <Chip
                icon={<Forum />}
                label={`${channelStats.length} channels`}
                variant="outlined"
                size="small"
              />
              <Chip
                icon={<Person />}
                label={`${userStats.length} users`}
                variant="outlined"
                size="small"
              />
            </>
          )}
        </ResultsStats>

        <Box display="flex" alignItems="center" gap={1} flexWrap="wrap">
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Sort by</InputLabel>
            <Select
              value={sortBy}
              label="Sort by"
              onChange={(e) =>
                setSortBy(
                  e.target.value as
                    | "timestamp"
                    | "relevance"
                    | "channel"
                    | "user"
                )
              }
            >
              <MenuItem value="timestamp">Time</MenuItem>
              <MenuItem value="relevance">Relevance</MenuItem>
              {isGlobalSearch && <MenuItem value="channel">Channel</MenuItem>}
              {isGlobalSearch && <MenuItem value="user">User</MenuItem>}
            </Select>
          </FormControl>

          <IconButton
            onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
            title={`Sort ${sortOrder === "asc" ? "descending" : "ascending"}`}
          >
            <Sort />
          </IconButton>

          <Tooltip title={showContext ? "Hide context" : "Show context"}>
            <IconButton
              onClick={() => setShowContext(!showContext)}
              color={showContext ? "primary" : "default"}
            >
              {showContext ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </Tooltip>

          <Button startIcon={<Download />} onClick={handleExport} size="small">
            Export
          </Button>

          {onClearSearch && (
            <Button onClick={onClearSearch} size="small" color="secondary">
              Clear Search
            </Button>
          )}

          {onShare && (
            <Button startIcon={<Share />} onClick={onShare} size="small">
              Share
            </Button>
          )}
        </Box>
      </ResultsHeader>

      {/* Channel and User Filtering */}
      {isGlobalSearch && (
        <Paper sx={{ p: 2, mb: 2 }}>
          <Typography
            variant="h6"
            gutterBottom
            display="flex"
            alignItems="center"
            gap={1}
          >
            <FilterList />
            Filters & Analytics
          </Typography>

          <Grid container spacing={2}>
            {/* Channel Filter */}
            <Grid item xs={12} md={6}>
              <Autocomplete
                multiple
                options={availableChannels}
                value={selectedChannels}
                onChange={(_, newValue) => setSelectedChannels(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Filter by Channels"
                    placeholder="Select channels..."
                    size="small"
                  />
                )}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={`#${option}`}
                      size="small"
                      {...getTagProps({ index })}
                      key={option}
                    />
                  ))
                }
              />
            </Grid>

            {/* User Filter */}
            <Grid item xs={12} md={6}>
              <Autocomplete
                multiple
                options={availableUsers}
                value={selectedUsers}
                onChange={(_, newValue) => setSelectedUsers(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Filter by Users"
                    placeholder="Select users..."
                    size="small"
                  />
                )}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={option}
                      size="small"
                      {...getTagProps({ index })}
                      key={option}
                    />
                  ))
                }
              />
            </Grid>
          </Grid>

          {/* Quick Stats */}
          <Box mt={2}>
            <Typography variant="subtitle2" gutterBottom>
              Top Channels by Activity
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
              {channelStats.slice(0, 5).map(({ channel, count }) => (
                <Chip
                  key={channel}
                  label={`#${channel} (${count})`}
                  size="small"
                  onClick={() => {
                    if (!selectedChannels.includes(channel)) {
                      setSelectedChannels([...selectedChannels, channel]);
                    }
                  }}
                  clickable
                  variant={
                    selectedChannels.includes(channel) ? "filled" : "outlined"
                  }
                  color={
                    selectedChannels.includes(channel) ? "primary" : "default"
                  }
                />
              ))}
            </Box>

            <Typography variant="subtitle2" gutterBottom>
              Top Users by Activity
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={1}>
              {userStats.slice(0, 5).map(({ user, count }) => (
                <Chip
                  key={user}
                  label={`${user} (${count})`}
                  size="small"
                  onClick={() => {
                    if (!selectedUsers.includes(user)) {
                      setSelectedUsers([...selectedUsers, user]);
                    }
                  }}
                  clickable
                  variant={selectedUsers.includes(user) ? "filled" : "outlined"}
                  color={selectedUsers.includes(user) ? "primary" : "default"}
                />
              ))}
            </Box>

            {/* Clear Filters */}
            {(selectedChannels.length > 0 || selectedUsers.length > 0) && (
              <Box mt={2}>
                <Button
                  startIcon={<Clear />}
                  onClick={() => {
                    setSelectedChannels([]);
                    setSelectedUsers([]);
                  }}
                  size="small"
                  variant="outlined"
                >
                  Clear All Filters
                </Button>
              </Box>
            )}
          </Box>
        </Paper>
      )}

      <ResultsList>
        {paginatedResults.map((message, index) => {
          // Convert FullLogMessage to LogMessage for LogLine component
          const logMessage: LogMessage = {
            text: message.text,
            displayName: message.displayName,
            timestamp: new Date(message.timestamp),
            id: message.id,
            tags: message.tags,
            emotes:
              message.emotes ||
              parseEmotes(message.text, message.tags["emotes"] || ""),
          };

          return (
            <ResultItem key={message.id || `${startIndex + index}`}>
              {/* Enhanced message display with channel info */}
              <Box>
                {isGlobalSearch && (
                  <Box display="flex" alignItems="center" gap={1} mb={1}>
                    <Chip
                      label={`#${message.channel}`}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                    <Chip
                      label={message.username || message.displayName}
                      size="small"
                      color="secondary"
                      variant="outlined"
                    />
                    <Typography variant="caption" color="text.secondary">
                      {new Date(message.timestamp).toLocaleString()}
                    </Typography>
                  </Box>
                )}
                <LogLine message={logMessage} />
                {showContext && !isGlobalSearch && (
                  <Box mt={1} pl={2} sx={{ opacity: 0.7 }}>
                    <Typography variant="caption">
                      Channel: {state.currentChannel} | Time:{" "}
                      {new Date(message.timestamp).toLocaleString()}
                    </Typography>
                  </Box>
                )}
              </Box>
            </ResultItem>
          );
        })}
      </ResultsList>

      {totalPages > 1 && (
        <Box display="flex" justifyContent="center" mt={2}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={(_, page) => setCurrentPage(page)}
            color="primary"
            showFirstButton
            showLastButton
          />
        </Box>
      )}
    </SearchResultsContainer>
  );
}
