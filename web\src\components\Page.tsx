import React, { useContext } from "react";
import { Box, Container, Fade } from "@mui/material";
import { store } from "../store";
import { Filters } from "./Filters";
import { LogContainer } from "./LogContainer";
import { OptoutPanel } from "./Optout";

export function Page() {
  const { state } = useContext(store);

  return (
    <Box
      sx={{
        minHeight: "100vh",
        background: "linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%)",
        position: "relative",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background:
            "radial-gradient(circle at 20% 50%, rgba(25, 118, 210, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(156, 39, 176, 0.1) 0%, transparent 50%)",
          pointerEvents: "none",
        },
      }}
    >
      <Box sx={{ position: "relative", zIndex: 1 }}>
        <Filters />
        {state.showOptout && (
          <Fade in timeout={500}>
            <Container maxWidth="lg" sx={{ py: 2 }}>
              <OptoutPanel />
            </Container>
          </Fade>
        )}
        <LogContainer />
      </Box>
    </Box>
  );
}
